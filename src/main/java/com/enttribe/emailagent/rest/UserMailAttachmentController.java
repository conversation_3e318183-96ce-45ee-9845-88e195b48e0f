package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.service.UserMailAttachmentService;

/* import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses; */
import org.springframework.web.bind.annotation.*;

import java.sql.Date;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

/**
 * The type User mail attachment controller.
 *  <AUTHOR>
 */
@RestController("UserMailAttachmentController")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/userMailAttachmentController")
public class UserMailAttachmentController {

    @Autowired
    UserMailAttachmentService userMailAttachmentService;

    private static final Logger log = EmailAgentLogger.getLogger(UserMailAttachmentController.class);

    @PostMapping("create")
    @Operation(summary = "Create User Mail Attachment", tags = "User Mail Attachment", description = "Creates a new User Mail Attachment record.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User mail attachment successfully created."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while creating the user mail attachment.")
    })
    public UserMailAttachment createUserMailAttachment(@RequestBody UserMailAttachment userMailAttachment) {
        return userMailAttachmentService.save(userMailAttachment);
    }

    @GetMapping("/getUserMailAttachmentById/{id}")
    @Operation(summary = "Get User Mail Attachment by ID", tags = "User Mail Attachment", description = "Fetches a User Mail Attachment by its ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User mail attachment successfully retrieved."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the user mail attachment.")
    })
    public UserMailAttachment getUserMailAttachmentById(@PathVariable Integer id) {
        return userMailAttachmentService.findById(id);
    }

    @PostMapping("updateUserMailAttachment/{id}")
    @Operation(summary = "Update User Mail Attachment", tags = "User Mail Attachment", description = "Updates an existing User Mail Attachment with the specified ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User mail attachment successfully updated."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the user mail attachment.")
    })
    public UserMailAttachment updateUserMailAttachment(@PathVariable Integer id, @RequestBody UserMailAttachment userMailAttachment) {
        return userMailAttachmentService.update(id, userMailAttachment);
    }

    @PostMapping("updateStatus/{id}")
    @Operation(summary = "Update User Mail Attachment Status", tags = "User Mail Attachment", description = "Updates the status of an existing User Mail Attachment, marking it as deleted or not.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User mail attachment status successfully updated."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the user mail attachment status.")
    })
    public String updateStatus(@PathVariable Integer id, @RequestParam boolean deleted) {
        return userMailAttachmentService.updateStatus(id, deleted);
    }

    @GetMapping("/search")
    @Operation(summary = "Search User Mail Attachments", tags = "User Mail Attachment", description = "Searches for User Mail Attachments based on various optional filters such as ID, userId, name, and other parameters.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search completed successfully, returning matching user mail attachments."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while performing the search.")
    })
    public List<UserMailAttachment> search(
        @RequestParam(name = "id", required = false) Integer id,
        @RequestParam(name = "userId", required = false) String userId,
        @RequestParam(name = "name", required = false) String name,
        @RequestParam(name = "uniqueName", required = false) String uniqueName,
        @RequestParam(name = "type", required = false) String type,
        @RequestParam(name = "messageId", required = false) String messageId,
        @RequestParam(name = "attachmentId", required = false) String attachmentId,
        @RequestParam(name = "ragDocumentId", required = false) String ragDocumentId,
        @RequestParam(name = "conversationId", required = false) String conversationId,
        @RequestParam(name = "internetMessageId", required = false) String internetMessageId,
        @RequestParam(name = "processingStatus", required = false) String processingStatus,
        @RequestParam(name = "processingError", required = false) String processingError,
        @RequestParam(name = "creationTime", required = false) Date creationTime,
        @RequestParam(name = "modifiedTime", required = false) Date modifiedTime)
     {
        log.info("Fetching all search logs");
        return userMailAttachmentService.search(id, userId, name, uniqueName, type, messageId, attachmentId, ragDocumentId, conversationId,internetMessageId,processingStatus,processingError,creationTime,modifiedTime);
     }


    @GetMapping("/count")
    @Operation(summary = "Count User Mail Attachments", tags = "User Mail Attachment", description = "Counts the number of User Mail Attachments that match the specified optional filter criteria such as ID, userId, name, and more.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Count retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while counting the user mail attachments.")
    })
    public long count (
        @RequestParam(name = "id", required = false) Integer id,
        @RequestParam(name = "userId", required = false) String userId,
        @RequestParam(name = "name", required = false) String name,
        @RequestParam(name = "uniqueName", required = false) String uniqueName,
        @RequestParam(name = "type", required = false) String type,
        @RequestParam(name = "messageId", required = false) String messageId,
        @RequestParam(name = "attachmentId", required = false) String attachmentId,
        @RequestParam(name = "ragDocumentId", required = false) String ragDocumentId,
        @RequestParam(name = "conversationId", required = false) String conversationId,
        @RequestParam(name = "internetMessageId", required = false) String internetMessageId,
        @RequestParam(name = "processingStatus", required = false) String processingStatus,
        @RequestParam(name = "processingError", required = false) String processingError,
        @RequestParam(name = "creationTime", required = false) Date creationTime,
        @RequestParam(name = "modifiedTime", required = false) Date modifiedTime)
     {
        log.info("Fetching all search logs");
        return userMailAttachmentService.count(id, userId, name, uniqueName, type, messageId, attachmentId, ragDocumentId, conversationId,internetMessageId,processingStatus,processingError,creationTime,modifiedTime);
     }


    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "userMailAttachmentByFilter")
    @Operation(summary = "Fetch User Mail Attachments by Filter", tags = "User Mail Attachment", description = "Fetches a list of User Mail Attachments based on provided filter criteria, with optional limit parameters for the number of records.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User Mail Attachments retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching User Mail Attachments.")
    })
    List<UserMailAttachment> userMailAttachmentByFilter(
        @Parameter(name = "Minimum number of records required") @RequestParam(required = false, name = "llimit") Integer llimit,
        @Parameter(name = "Maximum number of records required") @RequestParam(required = false, name = "ulimit") Integer ulimit,
        @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
            return userMailAttachmentService.userMailAttachmentByFilter(llimit, ulimit, filterMap);  
    }
         


    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "userMailAttachmentCountByFilter")
    @Operation(summary = "Count User Mail Attachments by Filter", tags = "User Mail Attachment", description = "Counts the number of User Mail Attachments based on the provided filter criteria.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User Mail Attachment count retrieved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while counting User Mail Attachments.")
    })
    long userMailAttachmentCountByFilter(
        @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
            return userMailAttachmentService.userMailAttachmentCountByFilter( filterMap);  
    }

    @PostMapping(path = "reset")
    @Operation(summary = "Reset User Mail Attachment", tags = "User Mail Attachment", description = "Resets the status of a User Mail Attachment identified by the provided ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User Mail Attachment reset successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while resetting User Mail Attachment.")
    })
    String resetUsermailAttachment(@RequestParam(required = true, name = "id") Integer id){
        return userMailAttachmentService.reset(id);
    }

}
