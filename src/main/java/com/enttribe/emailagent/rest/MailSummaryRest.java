package com.enttribe.emailagent.rest;

import com.azure.core.annotation.QueryParam;
import com.enttribe.emailagent.entity.ContactBook;
import com.enttribe.emailagent.entity.MailSummary;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.service.ImailSummaryService;

import net.minidev.json.JSONObject;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

/**
 * The type Mail summary rest.
 *  <AUTHOR>
 */
@RestController("mailSummaryRest")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/mailSummary")
public class MailSummaryRest {

     private static final Logger log = EmailAgentLogger.getLogger(MailSummaryRest.class);

     @Autowired
     private ImailSummaryService service;

       @GetMapping("getSummary")
       /* @Operation(summary = "Retrieve Mail and Thread Summary", tags = "Email Operations", description = "Fetches the summary of an email along with its thread based on the provided userId and internetMessageId.")
       @ApiResponses(value = {
               @ApiResponse(responseCode = "200", description = "Successfully retrieved the mail and thread summary."),
               @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the mail summary.")
       }) */
    public Map<String, Object> mailSummary(@RequestParam String userId, @RequestParam String internetMessageId) {
        return service.getMailSummaryAndThreadSummary(userId, internetMessageId);
    }


    @GetMapping("getAllMailSummary")
    /* @Operation(summary = "Retrieve All Mail Summaries", tags = "Email Operations", description = "Fetches a list of mail summaries with optional limits on records and an optional folder name filter.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of mail summaries."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving mail summaries.")
    }) */
    public List<MailSummary> getAllMailSummary(@RequestParam(required = false, name = "llimit") Integer llimit,@RequestParam(required = false, name = "ulimit") Integer ulimit, @RequestParam(required = false) String folderName) {
        return service.getAllMailSummary(llimit,ulimit,folderName);
    }


    @GetMapping("/saveContacts")
    /* @Operation(summary = "Save Contacts Summary", tags = "Contact Operations", description = "Saves the contacts summary by invoking the related service method.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully saved contacts summary."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while saving contacts summary.")
    }) */
    public void saveContacts() {
        service.saveContactSummary();
    }
    
    @GetMapping("/updateBatch")
    /* @Operation(summary = "Update Batch by Average Time", tags = "Batch Operations", description = "Updates a batch based on the average time using the related service method.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Batch update operation completed successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the batch.")
    }) */
    public void updateBatch() {
        service.updateBatchByAvgTime();
    }

    @PostMapping("/mailSummarySave")
    /* @Operation(
        summary = "Save Mail Summary",
        tags = "Email Operations",
        description = "Saves a new mail summary record to the database"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully saved the mail summary"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "500", description = "Internal server error occurred while saving the mail summary")
    }) */
    public Map<String, String> saveMailSummary(@RequestBody MailSummary mailSummary) {
        log.debug("Received request to save mail summary: {}", mailSummary);
        return service.saveMailSummary(mailSummary);
    }

    @PostMapping("/encryptExistingMailSummary")
    /* @Operation(
        summary = "Encrypt Existing Mail Summary",
        tags = "Email Operations",
        description = "Updates an existing mail summary record in the database"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated the mail summary"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the mail summary")
    }) */
    public Map<String, Object> encryptExistingMailSummary(@RequestBody Map<String, Integer> requestBody) {
        Integer startId = requestBody.get("startId");
        Integer endId = requestBody.get("endId");
        log.debug("Received request to encrypt mail summary: {}", requestBody);
        return service.encryptExistingMailSummary(startId, endId);
    }

    @PostMapping("/encryptExistingThreadSummary")
    /* @Operation(
        summary = "Encrypt Existing Thread Summary",
        tags = "Email Operations",
        description = "Updates an existing thread summary record in the database"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated the thread summary"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the thread summary")
    }) */
    public Map<String, Object> encryptExistingThreadSummary(@RequestBody Map<String, Integer> requestBody) {
        Integer startId = requestBody.get("startId");
        Integer endId = requestBody.get("endId");
        log.debug("Received request to encrypt thread summary: {}", requestBody);
        return service.encryptExistingThreadSummary(startId, endId);
    }

}
