package com.enttribe.emailagent.rest;

import java.util.List;
import java.util.Map;

/* import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses; */
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.enttribe.emailagent.dto.UserFolderDto;
import com.enttribe.emailagent.entity.UserFolders;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.service.UserFoldersService;

/* import io.swagger.v3.oas.annotations.Parameter; */

/**
 * The type User folder rest.
 *  <AUTHOR>
 */
@RestController("userFolder")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/userFolder")
public class UserFolderRest {

        private static final Logger log = EmailAgentLogger.getLogger(FailureLogsRestController.class);

        @Autowired
        private UserFoldersService service;

        @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, path = "userFolderByFilter")
        /* @Operation(summary = "Get User Folders by Filter", tags = "User Folders", description = "Fetches a list of user folders based on the provided filters, with optional limits on the number of records.")
        @ApiResponses(value = {
                @ApiResponse(responseCode = "200", description = "User folders successfully fetched based on the filters."),
                @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching user folders.")
        }) */
        public List<UserFolderDto> userFolderByFilter(
                @RequestParam(required = false, name = "llimit") Integer llimit,
                @RequestParam(required = false, name = "ulimit") Integer ulimit,
                @RequestBody Map<String, Object> filterMap) {
                    log.info("filter map {}", filterMap);
                    return service.getFoldersGroupByEmail(llimit, ulimit, filterMap);
        }

        @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, path = "userFolderCountByFilter")
        /* @Operation(summary = "Get User Folder Count by Filter", tags = "User Folders", description = "Fetches the count of user folders based on the provided filters.")
        @ApiResponses(value = {
                @ApiResponse(responseCode = "200", description = "User folder count successfully fetched based on the filters."),
                @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the user folder count.")
        }) */
        public long userFolderCountByFilter(
                @RequestBody Map<String, Object> filterMap) {
                    log.info("filter map {}", filterMap);
                    return service.getFoldersCountGroupByEmail(filterMap);
        }
        

}
